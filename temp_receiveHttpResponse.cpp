// 临时文件：干净的receiveHttpResponse函数
// 接收HTTP响应
QString UltraFastTLS::receiveHttpResponse(ConnectionInfo* connectionInfo)
{
    if (!connectionInfo || !connectionInfo->isValid) {
        return QString();
    }

#ifdef OPENSSL_FOUND
    if (!connectionInfo->ssl) {
        return QString();
    }

    QByteArray responseData;
    char buffer[8192];

    qDebug() << "🚀 基于'包同时到达'观察，一次性读取所有数据";

    // 🚀 简化策略：基于用户观察，包都是同时到达的，一次读取即可
    int received = SSL_read(connectionInfo->ssl, buffer, sizeof(buffer) - 1);
    if (received > 0) {
        buffer[received] = '\0';
        responseData.append(buffer, received);
        qDebug() << "📦 一次性读取:" << received << "bytes";
    } else if (received <= 0) {
        int error = SSL_get_error(connectionInfo->ssl, received);
        qDebug() << "🔍 SSL_read返回:" << received << "错误码:" << error;
        if (error == SSL_ERROR_ZERO_RETURN) {
            qDebug() << "🔌 连接被对方关闭";
        }
        // 即使没有数据也继续处理，可能是连接关闭但有缓存数据
    }

    // 🚀 直接解析HTTP响应，不需要复杂判断
    qDebug() << "✅ 数据读取完成，总大小:" << responseData.size() << "bytes，开始解析";

    // 🔥 第二步调试：检查receiveHttpResponse的返回值
    QString result = parseHttpResponse(QString::fromLatin1(responseData), connectionInfo);
    qDebug() << "🔥 receiveHttpResponse即将返回:" << result.length() << "字符";
    if (result.length() > 0) {
        qDebug() << "🔥 receiveHttpResponse返回预览:" << result.left(100) + "...";
    } else {
        qDebug() << "🔥 警告：receiveHttpResponse返回为空！";
    }

    return result;
#else
    // Windows SChannel实现
    return QString();
#endif
}
